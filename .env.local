# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://dbmbjzabnvokmchopbsc.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRibWJqemFibnZva21jaG9wYnNjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxMTIyODksImV4cCI6MjA2NDY4ODI4OX0.0WbQfTJS0onXh8tMLt-jjWF4DotXnRx7-bodBwbK3a8
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRibWJqemFibnZva21jaG9wYnNjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTExMjI4OSwiZXhwIjoyMDY0Njg4Mjg5fQ.7w4LPZrgLwUqtnMruoY78ph4PiqQdoYvYfMN_2HHwPQ

# Database URL for Drizzle (using pooler)
DATABASE_URL=postgresql://postgres.dbmbjzabnvokmchopbsc:<EMAIL>:6543/postgres

# Next.js
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=http://localhost:3000
