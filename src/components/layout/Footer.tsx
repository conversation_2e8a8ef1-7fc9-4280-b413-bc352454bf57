import Link from 'next/link';

export function Footer() {
  return (
    <footer className="bg-gray-50 border-t">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and description */}
          <div className="col-span-1 md:col-span-2">
            <Link href="/" className="text-2xl font-bold text-primary-600">
              Infauna
            </Link>
            <p className="mt-4 text-gray-600 max-w-md">
              Největší inzertní portál pro zvířata v České republice, na Slovensku a v EU. 
              Najděte svého nového společníka nebo nabídněte zvířata k prodeji.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase">
              R<PERSON><PERSON> odkazy
            </h3>
            <ul className="mt-4 space-y-4">
              <li>
                <Link href="/kategorie" className="text-base text-gray-600 hover:text-primary-600">
                  Kategorie
                </Link>
              </li>
              <li>
                <Link href="/vyhledavani" className="text-base text-gray-600 hover:text-primary-600">
                  Vyhledávání
                </Link>
              </li>
              <li>
                <Link href="/pridat-inzerat" className="text-base text-gray-600 hover:text-primary-600">
                  Přidat inzerát
                </Link>
              </li>
              <li>
                <Link href="/profil" className="text-base text-gray-600 hover:text-primary-600">
                  Můj profil
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase">
              Podpora
            </h3>
            <ul className="mt-4 space-y-4">
              <li>
                <Link href="/napoveda" className="text-base text-gray-600 hover:text-primary-600">
                  Nápověda
                </Link>
              </li>
              <li>
                <Link href="/kontakt" className="text-base text-gray-600 hover:text-primary-600">
                  Kontakt
                </Link>
              </li>
              <li>
                <Link href="/podminky" className="text-base text-gray-600 hover:text-primary-600">
                  Podmínky použití
                </Link>
              </li>
              <li>
                <Link href="/soukromi" className="text-base text-gray-600 hover:text-primary-600">
                  Ochrana soukromí
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t border-gray-200">
          <p className="text-base text-gray-400 text-center">
            &copy; {new Date().getFullYear()} Infauna. Všechna práva vyhrazena.
          </p>
        </div>
      </div>
    </footer>
  );
}
