'use client';

import { useState, useEffect } from 'react';
import { AdFilters } from '@/types';
import { Button } from '@/components/ui/Button';
import { X, Filter, ChevronDown, ChevronUp } from 'lucide-react';

interface AdsFiltersProps {
  filters: AdFilters;
  onFiltersChange: (filters: AdFilters) => void;
  categoryId?: number;
}

interface Category {
  id: number;
  name: string;
  slug: string;
}

interface Breed {
  id: number;
  name: string;
  slug: string;
  categoryId: number;
}

interface Region {
  id: number;
  name: string;
  slug: string;
}

export function AdsFilters({ filters, onFiltersChange, categoryId }: AdsFiltersProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [breeds, setBreeds] = useState<Breed[]>([]);
  const [regions, setRegions] = useState<Region[]>([]);
  const [localFilters, setLocalFilters] = useState<AdFilters>(filters);
  const [isExpanded, setIsExpanded] = useState(true);
  const [loadingBreeds, setLoadingBreeds] = useState(false);

  // Fetch categories
  useEffect(() => {
    fetch('/api/categories')
      .then(res => res.json())
      .then(setCategories)
      .catch(console.error);
  }, []);

  // Fetch regions
  useEffect(() => {
    fetch('/api/regions')
      .then(res => res.json())
      .then(setRegions)
      .catch(console.error);
  }, []);

  // Fetch breeds when category changes
  useEffect(() => {
    const selectedCategoryId = categoryId || localFilters.categoryId;
    if (selectedCategoryId) {
      setLoadingBreeds(true);
      fetch(`/api/breeds?categoryId=${selectedCategoryId}`)
        .then(res => res.json())
        .then(setBreeds)
        .catch(console.error)
        .finally(() => setLoadingBreeds(false));
    } else {
      setBreeds([]);
      setLoadingBreeds(false);
    }
  }, [localFilters.categoryId, categoryId]);

  const updateFilter = (key: keyof AdFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    
    // Reset breed when category changes
    if (key === 'categoryId') {
      newFilters.breedId = undefined;
    }
    
    setLocalFilters(newFilters);
    // Auto-apply filters for better UX
    onFiltersChange(newFilters);
  };

  const clearFilters = () => {
    const clearedFilters: AdFilters = categoryId ? { categoryId } : {};
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const hasActiveFilters = Object.keys(localFilters).some(key => {
    if (key === 'categoryId' && categoryId) return false; // Don't count pre-set category
    return localFilters[key as keyof AdFilters] !== undefined;
  });

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Filter className="w-5 h-5 text-primary-600 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">
              Filtry
            </h2>
            {hasActiveFilters && (
              <span className="ml-2 bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full">
                {Object.keys(localFilters).filter(key => {
                  if (key === 'categoryId' && categoryId) return false;
                  return localFilters[key as keyof AdFilters] !== undefined;
                }).length}
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="w-4 h-4 mr-1" />
                Vymazat
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-gray-500 hover:text-gray-700"
            >
              {isExpanded ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Filters Content */}
      {isExpanded && (
        <div className="p-6 space-y-6">
          <div className="text-center text-gray-500">
            <p className="text-lg">🚧 Nové filtry se připravují...</p>
            <p className="text-sm mt-2">Brzy zde budou moderní vyhledávací filtry s react-select!</p>
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2">Co bude nového:</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Pokročilé vyhledávání s našeptáváním</li>
                <li>• Moderní selecty s možností vyhledávání</li>
                <li>• Cenový filtr s rychlými rozsahy</li>
                <li>• Uložení oblíbených filtrů</li>
                <li>• Responzivní design pro mobily</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
