import Link from 'next/link';
import Image from 'next/image';
import { formatPrice, formatDate } from '@/lib/utils';
import { AdWithDetails } from '@/types';
import { MapPin, Calendar, Phone } from 'lucide-react';

interface AdCardProps {
  ad: AdWithDetails;
}

export function AdCard({ ad }: AdCardProps) {
  const mainImage = ad.images?.[0]?.imageUrl;
  
  return (
    <Link href={`/inzerat/${ad.slug}`} className="group">
      <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden">
        {/* Image */}
        <div className="aspect-video relative bg-gray-200">
          {mainImage ? (
            <Image
              src={mainImage}
              alt={ad.title}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-200"
            />
          ) : (
            <div className="flex items-center justify-center h-full text-gray-400">
              <span className="text-4xl">📷</span>
            </div>
          )}
          
          {/* Ad Type Badge */}
          <div className="absolute top-2 left-2">
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
              ad.adType === 'prodám' 
                ? 'bg-green-100 text-green-800'
                : ad.adType === 'koupím'
                ? 'bg-blue-100 text-blue-800'
                : 'bg-purple-100 text-purple-800'
            }`}>
              {ad.adType}
            </span>
          </div>
          
          {/* Price Badge */}
          {ad.priceType && ad.adType !== 'daruji' && (
            <div className="absolute top-2 right-2">
              <span className="bg-white/90 backdrop-blur-sm px-2 py-1 text-sm font-semibold rounded">
                {ad.priceAmount && (ad.priceType === 'CZK' || ad.priceType === 'EUR')
                  ? formatPrice(ad.priceAmount, ad.priceType)
                  : ad.priceType
                }
              </span>
            </div>
          )}
        </div>
        
        {/* Content */}
        <div className="p-4">
          <h3 className="font-semibold text-lg text-gray-900 mb-2 group-hover:text-primary-600 line-clamp-2">
            {ad.title}
          </h3>
          
          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
            {ad.description}
          </p>
          
          {/* Meta Information */}
          <div className="space-y-1 text-xs text-gray-500">
            <div className="flex items-center">
              <span className="font-medium text-primary-600">
                {ad.category.name}
              </span>
              {ad.breed && (
                <>
                  <span className="mx-1">•</span>
                  <span>{ad.breed.name}</span>
                </>
              )}
            </div>
            
            <div className="flex items-center">
              <MapPin className="w-3 h-3 mr-1" />
              <span>{ad.country}</span>
              {ad.region && (
                <>
                  <span className="mx-1">•</span>
                  <span>{ad.region.name}</span>
                </>
              )}
            </div>
            
            <div className="flex items-center">
              <Calendar className="w-3 h-3 mr-1" />
              <span>{formatDate(ad.createdAt)}</span>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}
