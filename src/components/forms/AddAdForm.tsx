'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { StepIndicator } from '@/components/ui/StepIndicator';
import { BasicInfoStep } from './BasicInfoStep';
import { ImageUploadStep } from './ImageUploadStep';
import { AdFormData, AdFormErrors, AD_FORM_STEPS } from '@/types/ad-form';
import { ArrowLeft, ArrowRight, Check, Loader2 } from 'lucide-react';
import { useAuth } from '@/components/auth/AuthProvider';

export function AddAdForm() {
  const router = useRouter();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState('basic-info');
  const [completedSteps, setCompletedSteps] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [createdAdId, setCreatedAdId] = useState<number | null>(null);

  const [formData, setFormData] = useState<AdFormData>({
    title: '',
    adType: 'prodám',
    description: '',
    country: 'ČR',
    categoryId: 0,
    contactPhone: '',
  });

  const [errors, setErrors] = useState<AdFormErrors>({});

  const validateBasicInfo = (): boolean => {
    const newErrors: AdFormErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Nadpis je povinný';
    } else if (formData.title.length < 5) {
      newErrors.title = 'Nadpis musí mít alespoň 5 znaků';
    }

    if (!formData.adType) {
      newErrors.adType = 'Typ inzerátu je povinný';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Popis je povinný';
    } else if (formData.description.length < 20) {
      newErrors.description = 'Popis musí mít alespoň 20 znaků';
    }

    if (!formData.country) {
      newErrors.country = 'Země je povinná';
    }

    if (!formData.categoryId) {
      newErrors.categoryId = 'Kategorie je povinná';
    }

    if (!formData.contactPhone.trim()) {
      newErrors.contactPhone = 'Kontaktní telefon je povinný';
    } else if (!/^(\+420|420)?\s*[0-9]{3}\s*[0-9]{3}\s*[0-9]{3}$/.test(formData.contactPhone.replace(/\s/g, ''))) {
      newErrors.contactPhone = 'Neplatný formát telefonu';
    }

    if (formData.priceType === 'CZK' || formData.priceType === 'EUR') {
      if (!formData.priceAmount || formData.priceAmount <= 0) {
        newErrors.priceAmount = 'Zadejte platnou částku';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleFieldChange = (field: keyof AdFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleNext = async () => {
    if (currentStep === 'basic-info') {
      if (!validateBasicInfo()) {
        return;
      }

      // Create the ad in database
      setLoading(true);
      try {
        // For testing, use a mock user ID
        const userId = user?.id || 'test-user-id';

        const response = await fetch('/api/ads', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...formData,
            userId,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to create ad');
        }

        const newAd = await response.json();
        setCreatedAdId(newAd.id);
        setCompletedSteps(prev => [...prev, 'basic-info']);
        setCurrentStep('images');
      } catch (error) {
        console.error('Error creating ad:', error);
        // Show error message
      } finally {
        setLoading(false);
      }
    }
  };

  const handleBack = () => {
    if (currentStep === 'images') {
      setCurrentStep('basic-info');
    }
  };

  const handleFinish = () => {
    if (createdAdId) {
      router.push(`/inzerat/${createdAdId}`);
    }
  };

  const handleSkipImages = () => {
    handleFinish();
  };

  const currentStepIndex = AD_FORM_STEPS.findIndex(step => step.id === currentStep);
  const isLastStep = currentStepIndex === AD_FORM_STEPS.length - 1;

  // For testing purposes, allow form without authentication
  // if (!user) {
  //   return (
  //     <div className="text-center py-12">
  //       <h2 className="text-2xl font-bold text-gray-900 mb-4">
  //         Přihlášení vyžadováno
  //       </h2>
  //       <p className="text-gray-600 mb-6">
  //         Pro přidání inzerátu se musíte nejdříve přihlásit
  //       </p>
  //       <Button onClick={() => router.push('/auth/login')}>
  //         Přihlásit se
  //       </Button>
  //     </div>
  //   );
  // }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Step Indicator */}
      <div className="mb-8">
        <StepIndicator
          steps={AD_FORM_STEPS}
          currentStep={currentStep}
          completedSteps={completedSteps}
        />
      </div>

      {/* Form Content */}
      <div className="bg-white rounded-lg shadow-lg p-6 md:p-8">
        {currentStep === 'basic-info' && (
          <BasicInfoStep
            data={formData}
            errors={errors}
            onChange={handleFieldChange}
          />
        )}

        {currentStep === 'images' && createdAdId && (
          <ImageUploadStep
            adId={createdAdId}
            onImagesUploaded={() => {
              setCompletedSteps(prev => [...prev, 'images']);
            }}
          />
        )}

        {/* Navigation */}
        <div className="flex justify-between items-center mt-8 pt-6 border-t">
          <div>
            {currentStep !== 'basic-info' && (
              <Button
                variant="outline"
                onClick={handleBack}
                disabled={loading}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Zpět
              </Button>
            )}
          </div>

          <div className="flex space-x-3">
            {currentStep === 'images' && (
              <Button
                variant="outline"
                onClick={handleSkipImages}
                disabled={loading}
              >
                Přeskočit fotografie
              </Button>
            )}

            {currentStep === 'basic-info' && (
              <Button
                onClick={handleNext}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Vytváření...
                  </>
                ) : (
                  <>
                    Pokračovat
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </>
                )}
              </Button>
            )}

            {currentStep === 'images' && completedSteps.includes('images') && (
              <Button onClick={handleFinish}>
                <Check className="w-4 h-4 mr-2" />
                Dokončit
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
