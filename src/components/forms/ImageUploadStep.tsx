import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/Button';
import { 
  Upload, 
  X, 
  Image as ImageIcon, 
  Camera,
  AlertCircle,
  CheckCircle 
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ImageFile {
  id: string;
  file: File;
  preview: string;
  uploading?: boolean;
  uploaded?: boolean;
  error?: string;
}

interface ImageUploadStepProps {
  adId: number;
  onImagesUploaded?: (imageUrls: string[]) => void;
}

export function ImageUploadStep({ adId, onImagesUploaded }: ImageUploadStepProps) {
  const [images, setImages] = useState<ImageFile[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);

  const maxImages = 10;
  const maxFileSize = 5 * 1024 * 1024; // 5MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  const validateFile = (file: File): string | null => {
    if (!allowedTypes.includes(file.type)) {
      return 'Podporované formáty: JPG, PNG, WebP';
    }
    if (file.size > maxFileSize) {
      return 'Maximální velikost souboru je 5MB';
    }
    return null;
  };

  const handleFiles = useCallback((files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const newImages: ImageFile[] = [];

    fileArray.forEach((file) => {
      if (images.length + newImages.length >= maxImages) return;

      const error = validateFile(file);
      if (error) {
        // Show error toast or notification
        console.error(`Error with file ${file.name}: ${error}`);
        return;
      }

      const id = Math.random().toString(36).substr(2, 9);
      const preview = URL.createObjectURL(file);
      
      newImages.push({
        id,
        file,
        preview,
      });
    });

    setImages(prev => [...prev, ...newImages]);
  }, [images.length]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
    
    if (e.dataTransfer.files) {
      handleFiles(e.dataTransfer.files);
    }
  }, [handleFiles]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  }, []);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(e.target.files);
    }
  };

  const removeImage = (id: string) => {
    setImages(prev => {
      const image = prev.find(img => img.id === id);
      if (image) {
        URL.revokeObjectURL(image.preview);
      }
      return prev.filter(img => img.id !== id);
    });
  };

  const uploadImages = async () => {
    if (images.length === 0) return;

    setUploading(true);
    const uploadedUrls: string[] = [];

    try {
      for (let i = 0; i < images.length; i++) {
        const image = images[i];
        
        // Update image state to show uploading
        setImages(prev => prev.map(img => 
          img.id === image.id ? { ...img, uploading: true } : img
        ));

        const formData = new FormData();
        formData.append('file', image.file);
        formData.append('adId', adId.toString());
        formData.append('order', i.toString());

        try {
          const response = await fetch('/api/upload/image', {
            method: 'POST',
            body: formData,
          });

          if (!response.ok) {
            throw new Error('Upload failed');
          }

          const result = await response.json();
          uploadedUrls.push(result.imageUrl);

          // Update image state to show success
          setImages(prev => prev.map(img => 
            img.id === image.id 
              ? { ...img, uploading: false, uploaded: true } 
              : img
          ));
        } catch (error) {
          // Update image state to show error
          setImages(prev => prev.map(img => 
            img.id === image.id 
              ? { ...img, uploading: false, error: 'Upload failed' } 
              : img
          ));
        }
      }

      onImagesUploaded?.(uploadedUrls);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Přidejte fotografie
        </h2>
        <p className="text-gray-600">
          Fotografie pomohou vašemu inzerátu získat více pozornosti
        </p>
      </div>

      {/* Upload Area */}
      <div
        className={cn(
          'border-2 border-dashed rounded-lg p-8 text-center transition-colors',
          dragActive 
            ? 'border-primary-500 bg-primary-50' 
            : 'border-gray-300 hover:border-gray-400'
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <div className="space-y-4">
          <div className="flex justify-center">
            <div className="p-3 bg-gray-100 rounded-full">
              <Camera className="w-8 h-8 text-gray-600" />
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Přetáhněte fotografie sem
            </h3>
            <p className="text-gray-600 mb-4">
              nebo klikněte pro výběr souborů
            </p>
            
            <input
              type="file"
              multiple
              accept="image/*"
              onChange={handleFileInput}
              className="hidden"
              id="file-upload"
            />
            
            <Button asChild variant="outline">
              <label htmlFor="file-upload" className="cursor-pointer">
                <Upload className="w-4 h-4 mr-2" />
                Vybrat fotografie
              </label>
            </Button>
          </div>
          
          <p className="text-xs text-gray-500">
            Maximálně {maxImages} fotografií, každá do 5MB (JPG, PNG, WebP)
          </p>
        </div>
      </div>

      {/* Image Preview Grid */}
      {images.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">
            Vybrané fotografie ({images.length}/{maxImages})
          </h3>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {images.map((image) => (
              <div key={image.id} className="relative group">
                <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                  <img
                    src={image.preview}
                    alt="Preview"
                    className="w-full h-full object-cover"
                  />
                  
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                    <Button
                      variant="destructive"
                      size="icon"
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() => removeImage(image.id)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                  
                  {/* Status Indicator */}
                  <div className="absolute top-2 right-2">
                    {image.uploading && (
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                        <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      </div>
                    )}
                    {image.uploaded && (
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <CheckCircle className="w-4 h-4 text-white" />
                      </div>
                    )}
                    {image.error && (
                      <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                        <AlertCircle className="w-4 h-4 text-white" />
                      </div>
                    )}
                  </div>
                </div>
                
                {image.error && (
                  <p className="text-xs text-red-600 mt-1">{image.error}</p>
                )}
              </div>
            ))}
          </div>
          
          {images.length > 0 && !uploading && (
            <div className="flex justify-center">
              <Button onClick={uploadImages} size="lg">
                <Upload className="w-4 h-4 mr-2" />
                Nahrát fotografie
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
