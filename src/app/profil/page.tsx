'use client';

import { Metada<PERSON> } from 'next';
import { Button } from '@/components/ui/Button';
import { User, Settings, Plus, Edit, Trash2 } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@/components/auth/AuthProvider';

export default function ProfilePage() {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Načítání...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow p-6 text-center">
          <User className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Přihlášení vyžadováno
          </h1>
          <p className="text-gray-600 mb-6">
            Pro přístup k profilu se musíte nejprve přihlásit.
          </p>
          <div className="space-y-3">
            <Button className="w-full" asChild>
              <Link href="/auth/login">
                Přihlásit se
              </Link>
            </Button>
            <Button variant="outline" className="w-full" asChild>
              <Link href="/auth/signup">
                Registrovat se
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Můj profil
              </h1>
              <p className="text-lg text-gray-600">
                Spravujte své inzeráty a nastavení
              </p>
            </div>
            <Button asChild>
              <Link href="/pridat-inzerat">
                <Plus className="w-4 h-4 mr-2" />
                Přidat inzerát
              </Link>
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-center mb-6">
                <div className="w-20 h-20 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <User className="w-10 h-10 text-gray-400" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900">
                  {user?.user_metadata?.full_name || 'Uživatel'}
                </h2>
                <p className="text-gray-600">
                  {user?.email}
                </p>
              </div>
              
              <nav className="space-y-2">
                <a href="#" className="flex items-center px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md">
                  <User className="w-4 h-4 mr-3" />
                  Profil
                </a>
                <a href="#" className="flex items-center px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md">
                  <Settings className="w-4 h-4 mr-3" />
                  Nastavení
                </a>
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* My Ads */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">
                  Moje inzeráty
                </h3>
              </div>
              
              <div className="p-6">
                {/* Empty state */}
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <Plus className="w-8 h-8 text-gray-400" />
                  </div>
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    Zatím nemáte žádné inzeráty
                  </h4>
                  <p className="text-gray-600 mb-6">
                    Začněte přidáním vašeho prvního inzerátu
                  </p>
                  <Button asChild>
                    <Link href="/pridat-inzerat">
                      <Plus className="w-4 h-4 mr-2" />
                      Přidat první inzerát
                    </Link>
                  </Button>
                </div>

                {/* TODO: Replace with actual ads list when user is authenticated */}
                {/* 
                <div className="space-y-4">
                  {userAds.map((ad) => (
                    <div key={ad.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold text-gray-900">{ad.title}</h4>
                          <p className="text-sm text-gray-600">{ad.category.name}</p>
                          <p className="text-sm text-gray-500">
                            Vytvořeno {formatDate(ad.createdAt)}
                          </p>
                        </div>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            <Edit className="w-4 h-4 mr-1" />
                            Upravit
                          </Button>
                          <Button variant="destructive" size="sm">
                            <Trash2 className="w-4 h-4 mr-1" />
                            Smazat
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                */}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
