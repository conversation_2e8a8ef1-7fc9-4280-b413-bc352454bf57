import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/Button';
import { Plus, Search, Heart, Shield, Users } from 'lucide-react';
import { db, categories } from '@/db';

export default async function HomePage() {
  // Use static categories for now - database works via API
  const allCategories = [
    { id: 1, name: '<PERSON><PERSON>', slug: 'psi' },
    { id: 2, name: '<PERSON><PERSON><PERSON>', slug: 'kocky' },
    { id: 3, name: '<PERSON><PERSON><PERSON><PERSON>', slug: 'ptaci' },
    { id: 4, name: 'Ostatn<PERSON>', slug: 'ostatni' },
  ];
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary-600 to-primary-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Najděte svého nového
              <span className="block text-primary-200">společníka</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-primary-100 max-w-3xl mx-auto">
              Největší inzertní portál pro zvířata v České republice, na Slovensku a v EU. 
              Psi, kočky, ptáci a další čekají na nový domov.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-primary-600 hover:bg-gray-100" asChild>
                <Link href="/vyhledavani">
                  <Search className="w-5 h-5 mr-2" />
                  Vyhledat zvířata
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600" asChild>
                <Link href="/pridat-inzerat">
                  <Plus className="w-5 h-5 mr-2" />
                  Přidat inzerát
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Kategorie zvířat
            </h2>
            <p className="text-lg text-gray-600">
              Vyberte si kategorii a najděte přesně to, co hledáte
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {allCategories.map((category, index) => {
              const icons = ['🐕', '🐱', '🦜', '🐰', '🐠', '🦎', '🐹'];
              const descriptions = [
                'Štěňata i dospělí psi všech plemen',
                'Koťata i dospělé kočky všech plemen',
                'Papoušci, kanáři a další ptactvo',
                'Králíci, morčata a další hlodavci',
                'Akvarijní a jezírkové ryby',
                'Plazi a obojživelníci',
                'Ostatní domácí zvířata'
              ];

              return (
                <CategoryCard
                  key={category.id}
                  title={category.name}
                  slug={category.slug}
                  description={descriptions[index] || 'Různá zvířata a jejich potřeby'}
                  icon={icons[index] || '🐾'}
                />
              );
            })}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Proč zvolit Infaunu?
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <FeatureCard 
              icon={<Heart className="w-8 h-8 text-primary-600" />}
              title="S láskou ke zvířatům"
              description="Každý inzerát prochází kontrolou, aby byla zajištěna péče o zvířata"
            />
            <FeatureCard 
              icon={<Shield className="w-8 h-8 text-primary-600" />}
              title="Bezpečné transakce"
              description="Ověření prodejci a bezpečné platební metody pro vaši ochranu"
            />
            <FeatureCard 
              icon={<Users className="w-8 h-8 text-primary-600" />}
              title="Aktivní komunita"
              description="Tisíce spokojených zákazníků a úspěšných adopcí každý měsíc"
            />
          </div>
        </div>
      </section>

      {/* Recent Ads Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Nejnovější inzeráty
            </h2>
            <p className="text-lg text-gray-600">
              Podívejte se na nejnovější nabídky zvířat
            </p>
          </div>
          
          {/* Recent ads will be loaded from API */}
          <div className="text-center">
            <p className="text-gray-500 mb-6">Načítání nejnovějších inzerátů...</p>
            <Button asChild>
              <Link href="/vyhledavani">
                Zobrazit všechny inzeráty
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}

function CategoryCard({ title, slug, description, icon }: {
  title: string;
  slug: string;
  description: string;
  icon: string;
}) {
  return (
    <Link href={`/kategorie/${slug}`} className="group">
      <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 text-center">
        <div className="text-4xl mb-4">{icon}</div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-primary-600">
          {title}
        </h3>
        <p className="text-gray-600">{description}</p>
      </div>
    </Link>
  );
}

function FeatureCard({ icon, title, description }: {
  icon: React.ReactNode;
  title: string;
  description: string;
}) {
  return (
    <div className="text-center">
      <div className="flex justify-center mb-4">
        {icon}
      </div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">
        {title}
      </h3>
      <p className="text-gray-600">
        {description}
      </p>
    </div>
  );
}
