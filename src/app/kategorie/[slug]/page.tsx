import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { db, categories } from '@/db';
import { eq } from 'drizzle-orm';
import { CategoryPageClient } from './CategoryPageClient';

interface CategoryPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const [category] = await db
    .select()
    .from(categories)
    .where(eq(categories.slug, params.slug));

  if (!category) {
    return {
      title: 'Kategorie nenalezena',
    };
  }

  return {
    title: `${category.name} - Infauna`,
    description: `Inzeráty v kategorii ${category.name}. Najděte nebo nabídněte zvířata.`,
  };
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const [category] = await db
    .select()
    .from(categories)
    .where(eq(categories.slug, params.slug));

  if (!category) {
    notFound();
  }

  return <CategoryPageClient category={category} />;
}
