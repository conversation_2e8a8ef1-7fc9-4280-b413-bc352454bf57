'use client';

import { useState } from 'react';
import { AdsList } from '@/components/ads/AdsList';
import { AdsFilters } from '@/components/ads/AdsFilters';
import { AdFilters, PaginationParams } from '@/types';

interface Category {
  id: number;
  name: string;
  slug: string;
}

interface CategoryPageClientProps {
  category: Category;
}

export function CategoryPageClient({ category }: CategoryPageClientProps) {
  const [filters, setFilters] = useState<AdFilters>({ categoryId: category.id });
  const [pagination, setPagination] = useState<PaginationParams>({
    sortBy: 'created_at',
    sortOrder: 'desc',
  });

  const handleFiltersChange = (newFilters: AdFilters) => {
    setFilters({ ...newFilters, categoryId: category.id });
  };

  const handleSortChange = (sortBy: string, sortOrder: string) => {
    setPagination({ ...pagination, sortBy: sortBy as any, sortOrder: sortOrder as any });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {category.name}
          </h1>
          <p className="text-lg text-gray-600">
            Procházejte inzeráty v kategorii {category.name}
          </p>
        </div>

        {/* Filters and Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <AdsFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              categoryId={category.id}
            />
          </div>

          {/* Ads Grid */}
          <div className="lg:col-span-3">
            {/* Sort Controls */}
            <div className="mb-6 flex justify-between items-center">
              <div></div>
              <select 
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
                value={`${pagination.sortBy}_${pagination.sortOrder}`}
                onChange={(e) => {
                  const [sortBy, sortOrder] = e.target.value.split('_');
                  handleSortChange(sortBy, sortOrder);
                }}
              >
                <option value="created_at_desc">Nejnovější</option>
                <option value="created_at_asc">Nejstarší</option>
                <option value="price_amount_asc">Cena vzestupně</option>
                <option value="price_amount_desc">Cena sestupně</option>
                <option value="title_asc">Název A-Z</option>
                <option value="title_desc">Název Z-A</option>
              </select>
            </div>

            {/* Ads List */}
            <AdsList filters={filters} pagination={pagination} />
          </div>
        </div>
      </div>
    </div>
  );
}
