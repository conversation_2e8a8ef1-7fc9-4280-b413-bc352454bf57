'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { LoginForm } from '@/components/auth/LoginForm';
import { SignUpForm } from '@/components/auth/SignUpForm';

export default function SignUpPage() {
  const [isLogin, setIsLogin] = useState(false);
  const router = useRouter();

  const handleSuccess = () => {
    router.push('/profil');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      {isLogin ? (
        <LoginForm
          onSuccess={handleSuccess}
          onSwitchToSignUp={() => setIsLogin(false)}
        />
      ) : (
        <SignUpForm
          onSuccess={handleSuccess}
          onSwitchToLogin={() => setIsLogin(true)}
        />
      )}
    </div>
  );
}
