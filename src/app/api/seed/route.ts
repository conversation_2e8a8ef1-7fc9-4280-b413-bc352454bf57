import { NextResponse } from 'next/server';
import { db, categories, breeds, regions } from '@/db';

const categoriesData = [
  { name: '<PERSON><PERSON>', slug: 'psi' },
  { name: '<PERSON><PERSON><PERSON>', slug: 'kocky' },
  { name: '<PERSON><PERSON><PERSON><PERSON>', slug: 'ptaci' },
  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', slug: 'hlo<PERSON><PERSON><PERSON>' },
  { name: '<PERSON><PERSON><PERSON>', slug: 'ryby' },
  { name: '<PERSON><PERSON><PERSON>', slug: 'plazi' },
  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', slug: 'ostatni' },
];

const regionsData = [
  { name: '<PERSON><PERSON><PERSON><PERSON> město Praha', slug: 'hlavni-mesto-praha' },
  { name: 'Středočeský kraj', slug: 'stredocesky-kraj' },
  { name: 'Ji<PERSON>č<PERSON>k<PERSON> kraj', slug: 'jihocesky-kraj' },
  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kraj', slug: 'plzensky-kraj' },
  { name: '<PERSON><PERSON><PERSON><PERSON> kraj', slug: 'karlovarsky-kraj' },
  { name: 'Ústecký kraj', slug: 'ustecky-kraj' },
  { name: 'Liberecký kraj', slug: 'liberecky-kraj' },
  { name: 'Kr<PERSON>lovéhradecký kraj', slug: 'kralovehradecky-kraj' },
  { name: 'Pardubický kraj', slug: 'pardubicky-kraj' },
  { name: 'Kraj Vysočina', slug: 'kraj-vysocina' },
  { name: 'Jihomoravský kraj', slug: 'jihomoravsky-kraj' },
  { name: 'Olomoucký kraj', slug: 'olomoucky-kraj' },
  { name: 'Zlínský kraj', slug: 'zlinsky-kraj' },
  { name: 'Moravskoslezský kraj', slug: 'moravskoslezsky-kraj' },
];

const breedsData = [
  // Psi (categoryId: 1)
  { name: 'Labradorský retrívr', slug: 'labradorsky-retrivr', categoryId: 1 },
  { name: 'Německý ovčák', slug: 'nemecky-ovcak', categoryId: 1 },
  { name: 'Golden retrívr', slug: 'golden-retrivr', categoryId: 1 },
  { name: 'Francouzský buldoček', slug: 'francouzsky-buldocek', categoryId: 1 },
  { name: 'Beagle', slug: 'beagle', categoryId: 1 },
  
  // Kočky (categoryId: 2)
  { name: 'Britská krátkosrstá', slug: 'britska-kratkosrsta', categoryId: 2 },
  { name: 'Perská', slug: 'perska', categoryId: 2 },
  { name: 'Maine Coon', slug: 'maine-coon', categoryId: 2 },
  { name: 'Ragdoll', slug: 'ragdoll', categoryId: 2 },
  { name: 'Siamská', slug: 'siamska', categoryId: 2 },
  
  // Ptáci (categoryId: 3)
  { name: 'Kanárek', slug: 'kanarek', categoryId: 3 },
  { name: 'Papoušek', slug: 'papousek', categoryId: 3 },
  { name: 'Andulka', slug: 'andulka', categoryId: 3 },
  
  // Hlodavci (categoryId: 4)
  { name: 'Králík', slug: 'kralik', categoryId: 4 },
  { name: 'Morče', slug: 'morce', categoryId: 4 },
  { name: 'Křeček', slug: 'krecek', categoryId: 4 },
];

export async function POST() {
  try {
    console.log('Starting seed process...');
    
    // Insert categories
    console.log('Inserting categories...');
    const insertedCategories = await db.insert(categories).values(categoriesData).returning();
    console.log(`Inserted ${insertedCategories.length} categories`);
    
    // Insert regions
    console.log('Inserting regions...');
    const insertedRegions = await db.insert(regions).values(regionsData).returning();
    console.log(`Inserted ${insertedRegions.length} regions`);
    
    // Insert breeds
    console.log('Inserting breeds...');
    const insertedBreeds = await db.insert(breeds).values(breedsData).returning();
    console.log(`Inserted ${insertedBreeds.length} breeds`);
    
    return NextResponse.json({
      status: 'success',
      message: 'Database seeded successfully',
      data: {
        categories: insertedCategories.length,
        regions: insertedRegions.length,
        breeds: insertedBreeds.length,
      }
    });
  } catch (error) {
    console.error('Seed error:', error);
    return NextResponse.json(
      { 
        status: 'error', 
        message: 'Seeding failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
