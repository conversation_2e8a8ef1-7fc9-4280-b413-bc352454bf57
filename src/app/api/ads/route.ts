import { NextRequest, NextResponse } from 'next/server';
import { db, ads, categories, breeds, regions, users, adImages } from '@/db';
import { eq, and, like, gte, lte, desc, asc } from 'drizzle-orm';
import { generateSlug } from '@/lib/utils';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Pagination
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const offset = (page - 1) * limit;
    
    // Filters
    const categoryId = searchParams.get('categoryId');
    const breedId = searchParams.get('breedId');
    const regionId = searchParams.get('regionId');
    const adType = searchParams.get('adType');
    const country = searchParams.get('country');
    const priceMin = searchParams.get('priceMin');
    const priceMax = searchParams.get('priceMax');
    const search = searchParams.get('search');
    
    // Sorting
    const sortBy = searchParams.get('sortBy') || 'created_at';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    
    // Build query
    let query = db
      .select({
        id: ads.id,
        title: ads.title,
        adType: ads.adType,
        country: ads.country,
        priceType: ads.priceType,
        priceAmount: ads.priceAmount,
        description: ads.description,
        contactPhone: ads.contactPhone,
        createdAt: ads.createdAt,
        updatedAt: ads.updatedAt,
        isActive: ads.isActive,
        slug: ads.slug,
        category: {
          id: categories.id,
          name: categories.name,
          slug: categories.slug,
        },
        breed: {
          id: breeds.id,
          name: breeds.name,
          slug: breeds.slug,
        },
        region: {
          id: regions.id,
          name: regions.name,
          slug: regions.slug,
        },
        user: {
          id: users.id,
          phoneNumber: users.phoneNumber,
        },
      })
      .from(ads)
      .leftJoin(categories, eq(ads.categoryId, categories.id))
      .leftJoin(breeds, eq(ads.breedId, breeds.id))
      .leftJoin(regions, eq(ads.regionId, regions.id))
      .leftJoin(users, eq(ads.userId, users.id))
      .where(eq(ads.isActive, true));
    
    // Apply filters
    const conditions = [eq(ads.isActive, true)];
    
    if (categoryId) {
      conditions.push(eq(ads.categoryId, parseInt(categoryId)));
    }
    
    if (breedId) {
      conditions.push(eq(ads.breedId, parseInt(breedId)));
    }
    
    if (regionId) {
      conditions.push(eq(ads.regionId, parseInt(regionId)));
    }
    
    if (adType) {
      conditions.push(eq(ads.adType, adType as any));
    }
    
    if (country) {
      conditions.push(eq(ads.country, country as any));
    }
    
    if (priceMin) {
      conditions.push(gte(ads.priceAmount, priceMin));
    }
    
    if (priceMax) {
      conditions.push(lte(ads.priceAmount, priceMax));
    }
    
    if (search) {
      conditions.push(like(ads.title, `%${search}%`));
    }
    
    if (conditions.length > 1) {
      query = query.where(and(...conditions));
    }
    
    // Apply sorting
    if (sortBy === 'created_at') {
      query = sortOrder === 'desc' ? query.orderBy(desc(ads.createdAt)) : query.orderBy(asc(ads.createdAt));
    } else if (sortBy === 'price_amount') {
      query = sortOrder === 'desc' ? query.orderBy(desc(ads.priceAmount)) : query.orderBy(asc(ads.priceAmount));
    } else if (sortBy === 'title') {
      query = sortOrder === 'desc' ? query.orderBy(desc(ads.title)) : query.orderBy(asc(ads.title));
    }
    
    // Apply pagination
    query = query.limit(limit).offset(offset);
    
    const results = await query;
    
    // Get images for each ad
    const adsWithImages = await Promise.all(
      results.map(async (ad) => {
        const images = await db
          .select()
          .from(adImages)
          .where(eq(adImages.adId, ad.id))
          .orderBy(asc(adImages.order));
        
        return {
          ...ad,
          images,
        };
      })
    );
    
    return NextResponse.json({
      ads: adsWithImages,
      pagination: {
        page,
        limit,
        hasMore: results.length === limit,
      },
    });
  } catch (error) {
    console.error('Error fetching ads:', error);
    return NextResponse.json(
      { error: 'Failed to fetch ads' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Generate slug from title
    const slug = generateSlug(body.title);

    // Handle guest users - create user if it doesn't exist
    let userId = body.userId;
    if (userId) {
      // Create user in database (for both guest and authenticated users)
      try {
        await db
          .insert(users)
          .values({
            id: userId,
            phoneNumber: body.contactPhone, // Use contact phone as phone number
          })
          .onConflictDoNothing(); // Don't fail if user already exists
      } catch (userError) {
        console.log('User might already exist:', userError);
      }
    }

    // Insert ad
    const [newAd] = await db
      .insert(ads)
      .values({
        ...body,
        slug,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return NextResponse.json(newAd, { status: 201 });
  } catch (error) {
    console.error('Error creating ad:', error);
    return NextResponse.json(
      { error: 'Failed to create ad' },
      { status: 500 }
    );
  }
}
