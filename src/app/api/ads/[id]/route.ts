import { NextRequest, NextResponse } from 'next/server';
import { db, ads, categories, breeds, regions, users, adImages } from '@/db';
import { eq } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adId = parseInt(params.id);
    
    const [ad] = await db
      .select({
        id: ads.id,
        title: ads.title,
        adType: ads.adType,
        country: ads.country,
        priceType: ads.priceType,
        priceAmount: ads.priceAmount,
        description: ads.description,
        contactPhone: ads.contactPhone,
        createdAt: ads.createdAt,
        updatedAt: ads.updatedAt,
        isActive: ads.isActive,
        slug: ads.slug,
        category: {
          id: categories.id,
          name: categories.name,
          slug: categories.slug,
        },
        breed: {
          id: breeds.id,
          name: breeds.name,
          slug: breeds.slug,
        },
        region: {
          id: regions.id,
          name: regions.name,
          slug: regions.slug,
        },
        user: {
          id: users.id,
          phoneNumber: users.phoneNumber,
        },
      })
      .from(ads)
      .leftJoin(categories, eq(ads.categoryId, categories.id))
      .leftJoin(breeds, eq(ads.breedId, breeds.id))
      .leftJoin(regions, eq(ads.regionId, regions.id))
      .leftJoin(users, eq(ads.userId, users.id))
      .where(eq(ads.id, adId));
    
    if (!ad) {
      return NextResponse.json(
        { error: 'Ad not found' },
        { status: 404 }
      );
    }
    
    // Get images
    const images = await db
      .select()
      .from(adImages)
      .where(eq(adImages.adId, adId))
      .orderBy(adImages.order);
    
    return NextResponse.json({
      ...ad,
      images,
    });
  } catch (error) {
    console.error('Error fetching ad:', error);
    return NextResponse.json(
      { error: 'Failed to fetch ad' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adId = parseInt(params.id);
    const body = await request.json();
    
    const [updatedAd] = await db
      .update(ads)
      .set({
        ...body,
        updatedAt: new Date(),
      })
      .where(eq(ads.id, adId))
      .returning();
    
    if (!updatedAd) {
      return NextResponse.json(
        { error: 'Ad not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(updatedAd);
  } catch (error) {
    console.error('Error updating ad:', error);
    return NextResponse.json(
      { error: 'Failed to update ad' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adId = parseInt(params.id);
    
    const [deletedAd] = await db
      .delete(ads)
      .where(eq(ads.id, adId))
      .returning();
    
    if (!deletedAd) {
      return NextResponse.json(
        { error: 'Ad not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ message: 'Ad deleted successfully' });
  } catch (error) {
    console.error('Error deleting ad:', error);
    return NextResponse.json(
      { error: 'Failed to delete ad' },
      { status: 500 }
    );
  }
}
