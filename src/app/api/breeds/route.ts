import { NextRequest, NextResponse } from 'next/server';
import { db, breeds } from '@/db';
import { eq } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('categoryId');
    
    let query = db.select().from(breeds);
    
    if (categoryId) {
      query = query.where(eq(breeds.categoryId, parseInt(categoryId)));
    }
    
    const allBreeds = await query;
    
    return NextResponse.json(allBreeds);
  } catch (error) {
    console.error('Error fetching breeds:', error);
    return NextResponse.json(
      { error: 'Failed to fetch breeds' },
      { status: 500 }
    );
  }
}
