import { NextResponse } from 'next/server';
import { db, regions } from '@/db';

export async function GET() {
  try {
    const allRegions = await db.select().from(regions);
    
    return NextResponse.json(allRegions);
  } catch (error) {
    console.error('Error fetching regions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch regions' },
      { status: 500 }
    );
  }
}
