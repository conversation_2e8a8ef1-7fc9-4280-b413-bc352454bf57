import { NextResponse } from 'next/server';
import { db } from '@/db';

export async function GET() {
  try {
    // Check if tables exist
    const tablesQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('categories', 'breeds', 'regions', 'ads', 'users', 'ad_images')
      ORDER BY table_name;
    `;
    
    const tables = await db.execute(tablesQuery);
    
    // Try to query categories table
    let categoriesData = null;
    try {
      const categoriesQuery = 'SELECT * FROM categories LIMIT 5';
      categoriesData = await db.execute(categoriesQuery);
    } catch (error) {
      console.log('Categories table not accessible:', error);
    }
    
    return NextResponse.json({
      status: 'success',
      tables: tables,
      categoriesData: categoriesData,
      message: `Found ${tables.length} tables`
    });
  } catch (error) {
    console.error('Tables test error:', error);
    return NextResponse.json(
      { 
        status: 'error', 
        message: 'Tables test failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
