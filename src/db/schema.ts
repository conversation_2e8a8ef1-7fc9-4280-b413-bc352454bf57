import { pgTable, serial, text, uuid, integer, decimal, boolean, timestamp, pgEnum } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// Enums
export const adTypeEnum = pgEnum('ad_type', ['prodám', 'koupím', 'daruji']);
export const countryEnum = pgEnum('country', ['ČR', 'SR', 'EU']);
export const priceTypeEnum = pgEnum('price_type', ['CZK', 'EUR', 'Dohodou', 'V textu']);

// Users table (extends Supabase auth.users)
export const users = pgTable('users', {
  id: uuid('id').primaryKey(), // References auth.users.id
  phoneNumber: text('phone_number'),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow(),
});

// Categories table
export const categories = pgTable('categories', {
  id: serial('id').primaryKey(),
  name: text('name').notNull().unique(),
  slug: text('slug').notNull().unique(),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
});

// Breeds table
export const breeds = pgTable('breeds', {
  id: serial('id').primaryKey(),
  name: text('name').notNull(),
  slug: text('slug').notNull().unique(),
  categoryId: integer('category_id').notNull().references(() => categories.id),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
});

// Regions table (Czech regions)
export const regions = pgTable('regions', {
  id: serial('id').primaryKey(),
  name: text('name').notNull().unique(),
  slug: text('slug').notNull().unique(),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
});

// Ads table
export const ads = pgTable('ads', {
  id: serial('id').primaryKey(),
  title: text('title').notNull(),
  adType: adTypeEnum('ad_type').notNull(),
  country: countryEnum('country').notNull(),
  regionId: integer('region_id').references(() => regions.id),
  categoryId: integer('category_id').notNull().references(() => categories.id),
  breedId: integer('breed_id').references(() => breeds.id),
  priceType: priceTypeEnum('price_type'),
  priceAmount: decimal('price_amount', { precision: 10, scale: 2 }),
  description: text('description').notNull(),
  contactPhone: text('contact_phone').notNull(),
  userId: uuid('user_id').notNull().references(() => users.id),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow(),
  isActive: boolean('is_active').default(true),
  slug: text('slug').notNull().unique(),
});

// Ad images table
export const adImages = pgTable('ad_images', {
  id: serial('id').primaryKey(),
  adId: integer('ad_id').notNull().references(() => ads.id, { onDelete: 'cascade' }),
  imageUrl: text('image_url').notNull(),
  order: integer('order').default(0),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  ads: many(ads),
}));

export const categoriesRelations = relations(categories, ({ many }) => ({
  breeds: many(breeds),
  ads: many(ads),
}));

export const breedsRelations = relations(breeds, ({ one, many }) => ({
  category: one(categories, {
    fields: [breeds.categoryId],
    references: [categories.id],
  }),
  ads: many(ads),
}));

export const regionsRelations = relations(regions, ({ many }) => ({
  ads: many(ads),
}));

export const adsRelations = relations(ads, ({ one, many }) => ({
  user: one(users, {
    fields: [ads.userId],
    references: [users.id],
  }),
  category: one(categories, {
    fields: [ads.categoryId],
    references: [categories.id],
  }),
  breed: one(breeds, {
    fields: [ads.breedId],
    references: [breeds.id],
  }),
  region: one(regions, {
    fields: [ads.regionId],
    references: [regions.id],
  }),
  images: many(adImages),
}));

export const adImagesRelations = relations(adImages, ({ one }) => ({
  ad: one(ads, {
    fields: [adImages.adId],
    references: [ads.id],
  }),
}));

// Types
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Category = typeof categories.$inferSelect;
export type NewCategory = typeof categories.$inferInsert;
export type Breed = typeof breeds.$inferSelect;
export type NewBreed = typeof breeds.$inferInsert;
export type Region = typeof regions.$inferSelect;
export type NewRegion = typeof regions.$inferInsert;
export type Ad = typeof ads.$inferSelect;
export type NewAd = typeof ads.$inferInsert;
export type AdImage = typeof adImages.$inferSelect;
export type NewAdImage = typeof adImages.$inferInsert;
