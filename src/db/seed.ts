import { config } from 'dotenv';
config({ path: '.env.local' });

import { db, categories, breeds, regions } from './index';
import { generateSlug } from '@/lib/utils';

const categoriesData = [
  { name: 'Psi', slug: 'psi' },
  { name: '<PERSON><PERSON><PERSON>', slug: 'kocky' },
  { name: '<PERSON><PERSON><PERSON><PERSON>', slug: 'ptaci' },
  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', slug: 'hlodavci' },
  { name: '<PERSON><PERSON><PERSON>', slug: 'ryby' },
  { name: '<PERSON><PERSON><PERSON>', slug: 'plazi' },
  { name: '<PERSON>statn<PERSON>', slug: 'ostatni' },
];

const breedsData = [
  // Psi (categoryId: 1)
  { name: 'Labradorsk<PERSON> retrívr', slug: 'labradorsky-retrivr', categoryId: 1 },
  { name: '<PERSON>ě<PERSON><PERSON><PERSON> ov<PERSON>', slug: 'nemecky-ovcak', categoryId: 1 },
  { name: 'Golden retrívr', slug: 'golden-retrivr', categoryId: 1 },
  { name: '<PERSON><PERSON><PERSON><PERSON> buldo<PERSON>', slug: 'fran<PERSON><PERSON>sky-buldoc<PERSON>', categoryId: 1 },
  { name: '<PERSON><PERSON><PERSON>', slug: 'beagle', categoryId: 1 },
  { name: 'Husky', slug: 'husky', categoryId: 1 },
  { name: 'Chihuahua', slug: 'chihuahua', categoryId: 1 },
  { name: 'Mops', slug: 'mops', categoryId: 1 },
  { name: 'Border kolie', slug: 'border-kolie', categoryId: 1 },
  { name: 'Jezevčík', slug: 'jezevcik', categoryId: 1 },
  
  // Kočky (categoryId: 2)
  { name: 'Britská krátkosrstá', slug: 'britska-kratkosrsta', categoryId: 2 },
  { name: 'Perská', slug: 'perska', categoryId: 2 },
  { name: 'Maine Coon', slug: 'maine-coon', categoryId: 2 },
  { name: 'Ragdoll', slug: 'ragdoll', categoryId: 2 },
  { name: 'Siamská', slug: 'siamska', categoryId: 2 },
  { name: 'Bengálská', slug: 'bengalska', categoryId: 2 },
  { name: 'Ruská modrá', slug: 'ruska-modra', categoryId: 2 },
  { name: 'Norská lesní', slug: 'norska-lesni', categoryId: 2 },
  { name: 'Sphynx', slug: 'sphynx', categoryId: 2 },
  { name: 'Domácí kočka', slug: 'domaci-kocka', categoryId: 2 },
  
  // Ptáci (categoryId: 3)
  { name: 'Kanárek', slug: 'kanarek', categoryId: 3 },
  { name: 'Papoušek', slug: 'papousek', categoryId: 3 },
  { name: 'Andulka', slug: 'andulka', categoryId: 3 },
  { name: 'Zebřička', slug: 'zebricka', categoryId: 3 },
  { name: 'Kakadu', slug: 'kakadu', categoryId: 3 },
  { name: 'Ara', slug: 'ara', categoryId: 3 },
  
  // Hlodavci (categoryId: 4)
  { name: 'Králík', slug: 'kralik', categoryId: 4 },
  { name: 'Morče', slug: 'morce', categoryId: 4 },
  { name: 'Křeček', slug: 'krecek', categoryId: 4 },
  { name: 'Potkan', slug: 'potkan', categoryId: 4 },
  { name: 'Myš', slug: 'mys', categoryId: 4 },
  { name: 'Činčila', slug: 'cincila', categoryId: 4 },
];

const regionsData = [
  { name: 'Hlavní město Praha', slug: 'hlavni-mesto-praha' },
  { name: 'Středočeský kraj', slug: 'stredocesky-kraj' },
  { name: 'Jihočeský kraj', slug: 'jihocesky-kraj' },
  { name: 'Plzeňský kraj', slug: 'plzensky-kraj' },
  { name: 'Karlovarský kraj', slug: 'karlovarsky-kraj' },
  { name: 'Ústecký kraj', slug: 'ustecky-kraj' },
  { name: 'Liberecký kraj', slug: 'liberecky-kraj' },
  { name: 'Královéhradecký kraj', slug: 'kralovehradecky-kraj' },
  { name: 'Pardubický kraj', slug: 'pardubicky-kraj' },
  { name: 'Kraj Vysočina', slug: 'kraj-vysocina' },
  { name: 'Jihomoravský kraj', slug: 'jihomoravsky-kraj' },
  { name: 'Olomoucký kraj', slug: 'olomoucky-kraj' },
  { name: 'Zlínský kraj', slug: 'zlinsky-kraj' },
  { name: 'Moravskoslezský kraj', slug: 'moravskoslezsky-kraj' },
];

export async function seedDatabase() {
  try {
    console.log('Seeding database...');
    
    // Insert categories
    console.log('Inserting categories...');
    const insertedCategories = await db.insert(categories).values(categoriesData).returning();
    console.log(`Inserted ${insertedCategories.length} categories`);
    
    // Insert breeds
    console.log('Inserting breeds...');
    const insertedBreeds = await db.insert(breeds).values(breedsData).returning();
    console.log(`Inserted ${insertedBreeds.length} breeds`);
    
    // Insert regions
    console.log('Inserting regions...');
    const insertedRegions = await db.insert(regions).values(regionsData).returning();
    console.log(`Inserted ${insertedRegions.length} regions`);
    
    console.log('Database seeded successfully!');
  } catch (error) {
    console.error('Error seeding database:', error);
    throw error;
  }
}

// Run seed if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Seeding failed:', error);
      process.exit(1);
    });
}
