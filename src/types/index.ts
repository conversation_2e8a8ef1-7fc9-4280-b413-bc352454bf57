export type AdType = 'prodám' | 'koupím' | 'daruji';
export type Country = 'ČR' | 'SR' | 'EU';
export type PriceType = 'CZK' | 'EUR' | 'Dohodou' | 'V textu';

export interface AdWithDetails {
  id: number;
  title: string;
  adType: AdType;
  country: Country;
  priceType?: PriceType;
  priceAmount?: string;
  description: string;
  contactPhone: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  slug: string;
  category: {
    id: number;
    name: string;
    slug: string;
  };
  breed?: {
    id: number;
    name: string;
    slug: string;
  };
  region?: {
    id: number;
    name: string;
    slug: string;
  };
  user: {
    id: string;
    phoneNumber?: string;
  };
  images: Array<{
    id: number;
    imageUrl: string;
    order: number;
  }>;
}

export interface AdFilters {
  categoryId?: number;
  breedId?: number;
  regionId?: number;
  adType?: AdType;
  country?: Country;
  priceMin?: number;
  priceMax?: number;
  search?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: 'created_at' | 'price_amount' | 'title';
  sortOrder?: 'asc' | 'desc';
}
