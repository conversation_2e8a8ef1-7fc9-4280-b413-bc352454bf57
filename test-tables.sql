-- Test if tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('categories', 'breeds', 'regions', 'ads', 'users', 'ad_images');

-- If no results, create a simple test table
CREATE TABLE IF NOT EXISTS test_categories (
  id serial PRIMARY KEY,
  name text NOT NULL,
  slug text NOT NULL UNIQUE
);

-- Insert test data
INSERT INTO test_categories (name, slug) VALUES 
('Psi', 'psi'),
('Kočky', 'kocky')
ON CONFLICT (slug) DO NOTHING;
