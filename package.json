{"name": "infauna", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:seed": "npx tsx src/db/seed.ts"}, "keywords": ["next.js", "typescript", "supabase", "drizzle", "animals", "marketplace"], "author": "", "license": "ISC", "description": "Webový inzertní portál pro zvířata", "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.10", "@tailwindcss/postcss": "^4.1.8", "@types/node": "^22.15.29", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.44.2", "lucide-react": "^0.513.0", "next": "^15.3.3", "postcss": "^8.5.4", "postgres": "^3.4.7", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "typescript": "^5.8.3"}, "devDependencies": {"dotenv": "^16.5.0", "tsx": "^4.19.4"}}