# Infauna - Inzertní portál pro zvířata

Webový inzertní portál pro zvířata vytvořený s Next.js, TypeScript, Drizzle ORM a Supabase.

## Technologie

- **Framework**: Next.js 15 (App Router)
- **Jazyk**: TypeScript
- **ORM**: Drizzle ORM
- **Databáze**: Supabase (PostgreSQL)
- **Autentizace**: Supabase Auth
- **Úložiště**: Supabase Storage
- **Styling**: Tailwind CSS
- **Ikony**: Lucide React

## Funkce

- ✅ Databázové schéma (uživatelé, kategorie, plemena, kraje, inzeráty, obrázky)
- ✅ API routes pro CRUD operace
- ✅ Responzivní design
- ✅ Homepage s hero sekcí a kategoriemi
- ✅ Stránka kategorie s filtry a řazením
- ✅ Formulář pro přidání inzerátu
- ✅ Detail inzerátu s galerií obr<PERSON>zků
- ✅ Vyhledávání s pokročilými filtry
- ✅ Komponenty pro zobrazení inzerátů
- ✅ SEO optimalizace (metadata, slugy)
- ⏳ Autentizace uživatelů (Supabase Auth)
- ⏳ Nahrávání obrázků (Supabase Storage)
- ⏳ Uživatelský profil s vlastními inzeráty
- ⏳ Funkční formulář pro přidání inzerátu

## Instalace

1. **Klonování projektu**
   ```bash
   git clone <repository-url>
   cd infauna
   ```

2. **Instalace závislostí**
   ```bash
   npm install
   ```

3. **Nastavení prostředí**
   ```bash
   cp .env.example .env.local
   ```
   
   Vyplňte následující proměnné v `.env.local`:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   DATABASE_URL=postgresql://postgres:[password]@db.[project_ref].supabase.co:5432/postgres
   ```

4. **Nastavení Supabase**
   - Vytvořte nový projekt na [supabase.com](https://supabase.com)
   - Zkopírujte URL a klíče do `.env.local`
   - Povolte Row Level Security (RLS) na tabulkách

5. **Migrace databáze**
   ```bash
   npm run db:generate
   npm run db:migrate
   ```

6. **Seed data**
   ```bash
   npm run db:seed
   ```

7. **Spuštění vývojového serveru**
   ```bash
   npm run dev
   ```

   Aplikace bude dostupná na `http://localhost:3000`

## Struktura projektu

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   ├── kategorie/         # Stránky kategorií
│   ├── pridat-inzerat/    # Formulář pro přidání inzerátu
│   ├── layout.tsx         # Hlavní layout
│   └── page.tsx           # Homepage
├── components/            # React komponenty
│   ├── layout/           # Layout komponenty
│   ├── ui/               # UI komponenty
│   ├── forms/            # Formulářové komponenty
│   └── ads/              # Komponenty pro inzeráty
├── db/                   # Databázové schéma a konfigurace
├── lib/                  # Utility funkce
└── types/                # TypeScript typy
```

## Databázové schéma

### Tabulky

- **users** - Uživatelé (rozšíření Supabase auth.users)
- **categories** - Kategorie zvířat (Psi, Kočky, atd.)
- **breeds** - Plemena/druhy zvířat
- **regions** - České kraje
- **ads** - Inzeráty
- **ad_images** - Obrázky k inzerátům

### Vztahy

- Kategorie → Plemena (1:N)
- Kategorie → Inzeráty (1:N)
- Plemena → Inzeráty (1:N)
- Kraje → Inzeráty (1:N)
- Uživatelé → Inzeráty (1:N)
- Inzeráty → Obrázky (1:N)

## API Endpointy

- `GET /api/categories` - Seznam kategorií
- `GET /api/breeds?categoryId=1` - Seznam plemen pro kategorii
- `GET /api/regions` - Seznam krajů
- `GET /api/ads` - Seznam inzerátů (s filtry a stránkováním)
- `POST /api/ads` - Vytvoření nového inzerátu
- `GET /api/ads/[id]` - Detail inzerátu
- `PUT /api/ads/[id]` - Aktualizace inzerátu
- `DELETE /api/ads/[id]` - Smazání inzerátu

## Další kroky

1. **Implementace autentizace**
   - Přihlášení/registrace
   - Ochrana routes
   - Správa session

2. **Nahrávání obrázků**
   - Integrace Supabase Storage
   - Optimalizace obrázků
   - Drag & drop interface

3. **Pokročilé funkce**
   - Fulltextové vyhledávání
   - Pokročilé filtry
   - Geolokace
   - Email notifikace

4. **SEO a výkon**
   - Metadata pro každou stránku
   - Sitemap
   - Optimalizace rychlosti

## Licence

MIT
