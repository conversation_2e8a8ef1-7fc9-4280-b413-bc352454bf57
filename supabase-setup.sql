-- Infauna Database Setup
-- Run this in Supabase SQL Editor

-- Create enums
CREATE TYPE "public"."ad_type" AS ENUM('prodám', 'koupím', 'da<PERSON><PERSON>');
CREATE TYPE "public"."country" AS ENUM('ČR', 'SR', 'EU');
CREATE TYPE "public"."price_type" AS ENUM('CZK', 'EUR', 'Dohodou', 'V textu');

-- Create tables
CREATE TABLE "public"."users" (
  "id" uuid PRIMARY KEY NOT NULL,
  "phone_number" text,
  "created_at" timestamp with time zone DEFAULT now(),
  "updated_at" timestamp with time zone DEFAULT now()
);

CREATE TABLE "public"."categories" (
  "id" serial PRIMARY KEY NOT NULL,
  "name" text NOT NULL,
  "slug" text NOT NULL,
  "created_at" timestamp with time zone DEFAULT now(),
  CONSTRAINT "categories_name_unique" UNIQUE("name"),
  CONSTRAINT "categories_slug_unique" UNIQUE("slug")
);

CREATE TABLE "public"."regions" (
  "id" serial PRIMARY KEY NOT NULL,
  "name" text NOT NULL,
  "slug" text NOT NULL,
  "created_at" timestamp with time zone DEFAULT now(),
  CONSTRAINT "regions_name_unique" UNIQUE("name"),
  CONSTRAINT "regions_slug_unique" UNIQUE("slug")
);

CREATE TABLE "public"."breeds" (
  "id" serial PRIMARY KEY NOT NULL,
  "name" text NOT NULL,
  "slug" text NOT NULL,
  "category_id" integer NOT NULL,
  "created_at" timestamp with time zone DEFAULT now(),
  CONSTRAINT "breeds_slug_unique" UNIQUE("slug")
);

CREATE TABLE "public"."ads" (
  "id" serial PRIMARY KEY NOT NULL,
  "title" text NOT NULL,
  "ad_type" "ad_type" NOT NULL,
  "country" "country" NOT NULL,
  "region_id" integer,
  "category_id" integer NOT NULL,
  "breed_id" integer,
  "price_type" "price_type",
  "price_amount" numeric(10, 2),
  "description" text NOT NULL,
  "contact_phone" text NOT NULL,
  "user_id" uuid NOT NULL,
  "created_at" timestamp with time zone DEFAULT now(),
  "updated_at" timestamp with time zone DEFAULT now(),
  "is_active" boolean DEFAULT true,
  "slug" text NOT NULL,
  CONSTRAINT "ads_slug_unique" UNIQUE("slug")
);

CREATE TABLE "public"."ad_images" (
  "id" serial PRIMARY KEY NOT NULL,
  "ad_id" integer NOT NULL,
  "image_url" text NOT NULL,
  "order" integer DEFAULT 0,
  "created_at" timestamp with time zone DEFAULT now()
);

-- Add foreign key constraints
ALTER TABLE "public"."breeds" ADD CONSTRAINT "breeds_category_id_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "public"."ads" ADD CONSTRAINT "ads_region_id_regions_id_fk" FOREIGN KEY ("region_id") REFERENCES "public"."regions"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "public"."ads" ADD CONSTRAINT "ads_category_id_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "public"."ads" ADD CONSTRAINT "ads_breed_id_breeds_id_fk" FOREIGN KEY ("breed_id") REFERENCES "public"."breeds"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "public"."ads" ADD CONSTRAINT "ads_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "public"."ad_images" ADD CONSTRAINT "ad_images_ad_id_ads_id_fk" FOREIGN KEY ("ad_id") REFERENCES "public"."ads"("id") ON DELETE cascade ON UPDATE no action;

-- Insert seed data
-- Categories
INSERT INTO "public"."categories" ("name", "slug") VALUES
('Psi', 'psi'),
('Kočky', 'kocky'),
('Ptáci', 'ptaci'),
('Hlodavci', 'hlodavci'),
('Ryby', 'ryby'),
('Plazi', 'plazi'),
('Ostatní', 'ostatni');

-- Regions (Czech regions)
INSERT INTO "public"."regions" ("name", "slug") VALUES
('Hlavní město Praha', 'hlavni-mesto-praha'),
('Středočeský kraj', 'stredocesky-kraj'),
('Jihočeský kraj', 'jihocesky-kraj'),
('Plzeňský kraj', 'plzensky-kraj'),
('Karlovarský kraj', 'karlovarsky-kraj'),
('Ústecký kraj', 'ustecky-kraj'),
('Liberecký kraj', 'liberecky-kraj'),
('Královéhradecký kraj', 'kralovehradecky-kraj'),
('Pardubický kraj', 'pardubicky-kraj'),
('Kraj Vysočina', 'kraj-vysocina'),
('Jihomoravský kraj', 'jihomoravsky-kraj'),
('Olomoucký kraj', 'olomoucky-kraj'),
('Zlínský kraj', 'zlinsky-kraj'),
('Moravskoslezský kraj', 'moravskoslezsky-kraj');

-- Breeds
INSERT INTO "public"."breeds" ("name", "slug", "category_id") VALUES
-- Psi (category_id: 1)
('Labradorský retrívr', 'labradorsky-retrivr', 1),
('Německý ovčák', 'nemecky-ovcak', 1),
('Golden retrívr', 'golden-retrivr', 1),
('Francouzský buldoček', 'francouzsky-buldocek', 1),
('Beagle', 'beagle', 1),
('Husky', 'husky', 1),
('Chihuahua', 'chihuahua', 1),
('Mops', 'mops', 1),
('Border kolie', 'border-kolie', 1),
('Jezevčík', 'jezevcik', 1),

-- Kočky (category_id: 2)
('Britská krátkosrstá', 'britska-kratkosrsta', 2),
('Perská', 'perska', 2),
('Maine Coon', 'maine-coon', 2),
('Ragdoll', 'ragdoll', 2),
('Siamská', 'siamska', 2),
('Bengálská', 'bengalska', 2),
('Ruská modrá', 'ruska-modra', 2),
('Norská lesní', 'norska-lesni', 2),
('Sphynx', 'sphynx', 2),
('Domácí kočka', 'domaci-kocka', 2),

-- Ptáci (category_id: 3)
('Kanárek', 'kanarek', 3),
('Papoušek', 'papousek', 3),
('Andulka', 'andulka', 3),
('Zebřička', 'zebricka', 3),
('Kakadu', 'kakadu', 3),
('Ara', 'ara', 3),

-- Hlodavci (category_id: 4)
('Králík', 'kralik', 4),
('Morče', 'morce', 4),
('Křeček', 'krecek', 4),
('Potkan', 'potkan', 4),
('Myš', 'mys', 4),
('Činčila', 'cincila', 4);

-- Enable Row Level Security (RLS)
ALTER TABLE "public"."users" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."ads" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."ad_images" ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can read their own data
CREATE POLICY "Users can view own profile" ON "public"."users"
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON "public"."users"
  FOR UPDATE USING (auth.uid() = id);

-- Anyone can read active ads
CREATE POLICY "Anyone can view active ads" ON "public"."ads"
  FOR SELECT USING (is_active = true);

-- Users can create ads
CREATE POLICY "Authenticated users can create ads" ON "public"."ads"
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own ads
CREATE POLICY "Users can update own ads" ON "public"."ads"
  FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own ads
CREATE POLICY "Users can delete own ads" ON "public"."ads"
  FOR DELETE USING (auth.uid() = user_id);

-- Ad images policies
CREATE POLICY "Anyone can view ad images" ON "public"."ad_images"
  FOR SELECT USING (true);

CREATE POLICY "Users can manage images for their ads" ON "public"."ad_images"
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM "public"."ads" 
      WHERE "ads"."id" = "ad_images"."ad_id" 
      AND "ads"."user_id" = auth.uid()
    )
  );

-- Create function to automatically create user profile
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.users (id, created_at, updated_at)
  VALUES (new.id, now(), now());
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
