# Supabase Setup Instructions

## Pot<PERSON><PERSON>n<PERSON> credentials

Pro dokončení nastavení potřebujeme získat ze Supabase dashboardu:

### 1. Database Password
- Jd<PERSON><PERSON> na https://supabase.com/dashboard/project/dbmbjzabnvokmchopbsc
- V levém menu klikněte na "Settings" → "Database"
- V sekci "Connection string" najděte heslo nebo ho resetujte
- Zkopírujte heslo a aktualizujte `DATABASE_URL` v `.env.local`

### 2. Service Role Key (pro API routes)
- V levém menu klikněte na "Settings" → "API"
- V sekci "Project API keys" najděte "service_role" key
- Zkopírujte ho a přidejte do `.env.local` jako `SUPABASE_SERVICE_ROLE_KEY`

## Aktuální .env.local

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://dbmbjzabnvokmchopbsc.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRibWJqemFibnZva21jaG9wYnNjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxMTIyODksImV4cCI6MjA2NDY4ODI4OX0.0WbQfTJS0onXh8tMLt-jjWF4DotXnRx7-bodBwbK3a8
SUPABASE_SERVICE_ROLE_KEY=[POTŘEBA DOPLNIT]

# Database URL for Drizzle
DATABASE_URL=postgresql://postgres.dbmbjzabnvokmchopbsc:[POTŘEBA DOPLNIT HESLO]@aws-0-eu-central-1.pooler.supabase.com:6543/postgres

# Next.js
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=http://localhost:3000
```

## Po získání credentials

1. **Spustit migrace:**
   ```bash
   npm run db:migrate
   ```

2. **Naplnit seed data:**
   ```bash
   npm run db:seed
   ```

3. **Ověřit připojení:**
   ```bash
   npm run dev
   ```

## Vygenerované migrace

✅ Migrace byla úspěšně vygenerována v `drizzle/0000_eminent_jetstream.sql`

Obsahuje:
- Všechny tabulky (users, categories, breeds, regions, ads, ad_images)
- Enums pro typy inzerátů, země a ceny
- Foreign key constraints
- Unique constraints pro slugy

## ✅ Implementováno

- **Autentizace**: Kompletní systém přihlášení/registrace
- **Ochrana routes**: Middleware pro chráněné stránky
- **UI komponenty**: Všechny potřebné komponenty
- **API endpointy**: Připravené pro databázi

## Další kroky po nastavení DB

1. **Testovat API endpointy s reálnými daty**
2. **Přidat nahrávání obrázků do Supabase Storage**
3. **Dokončit funkční formuláře**
4. **Testovat autentizaci s Google OAuth**

## Poznámky

- Všechny autentizační komponenty jsou připravené
- Middleware chrání routes `/profil` a `/pridat-inzerat`
- Header se dynamicky mění podle stavu přihlášení
- Používá se nejnovější @supabase/ssr balíček
