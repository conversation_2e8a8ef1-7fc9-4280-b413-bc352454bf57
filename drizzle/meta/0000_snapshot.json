{"id": "d3cbc1a9-1f5b-43a5-91d6-2e3bb78cba4b", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.ad_images": {"name": "ad_images", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "ad_id": {"name": "ad_id", "type": "integer", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"ad_images_ad_id_ads_id_fk": {"name": "ad_images_ad_id_ads_id_fk", "tableFrom": "ad_images", "tableTo": "ads", "columnsFrom": ["ad_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ads": {"name": "ads", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "ad_type": {"name": "ad_type", "type": "ad_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "country": {"name": "country", "type": "country", "typeSchema": "public", "primaryKey": false, "notNull": true}, "region_id": {"name": "region_id", "type": "integer", "primaryKey": false, "notNull": false}, "category_id": {"name": "category_id", "type": "integer", "primaryKey": false, "notNull": true}, "breed_id": {"name": "breed_id", "type": "integer", "primaryKey": false, "notNull": false}, "price_type": {"name": "price_type", "type": "price_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "price_amount": {"name": "price_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "contact_phone": {"name": "contact_phone", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"ads_region_id_regions_id_fk": {"name": "ads_region_id_regions_id_fk", "tableFrom": "ads", "tableTo": "regions", "columnsFrom": ["region_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ads_category_id_categories_id_fk": {"name": "ads_category_id_categories_id_fk", "tableFrom": "ads", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ads_breed_id_breeds_id_fk": {"name": "ads_breed_id_breeds_id_fk", "tableFrom": "ads", "tableTo": "breeds", "columnsFrom": ["breed_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ads_user_id_users_id_fk": {"name": "ads_user_id_users_id_fk", "tableFrom": "ads", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"ads_slug_unique": {"name": "ads_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.breeds": {"name": "breeds", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"breeds_category_id_categories_id_fk": {"name": "breeds_category_id_categories_id_fk", "tableFrom": "breeds", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"breeds_slug_unique": {"name": "breeds_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"categories_name_unique": {"name": "categories_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "categories_slug_unique": {"name": "categories_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.regions": {"name": "regions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"regions_name_unique": {"name": "regions_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "regions_slug_unique": {"name": "regions_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "phone_number": {"name": "phone_number", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.ad_type": {"name": "ad_type", "schema": "public", "values": ["prodám", "koupím", "<PERSON><PERSON><PERSON>"]}, "public.country": {"name": "country", "schema": "public", "values": ["ČR", "SR", "EU"]}, "public.price_type": {"name": "price_type", "schema": "public", "values": ["CZK", "EUR", "Dohodou", "V textu"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}